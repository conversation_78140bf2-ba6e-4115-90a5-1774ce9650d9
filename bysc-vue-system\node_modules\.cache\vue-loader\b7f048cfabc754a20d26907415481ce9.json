{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue?vue&type=template&id=2b0a37d0&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue", "mtime": 1753782522395}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<div style=\"padding: 20px;\">\n  <h2>租户页面调试工具</h2>\n  \n  <div style=\"margin-bottom: 20px;\">\n    <h3>Loading 状态检查</h3>\n    <p>Grid loading: {{ gridLoading }}</p>\n    <p>Store loadingShow: {{ $store.state.common.loadingShow }}</p>\n    <p>App loadings: {{ appLoadings }}</p>\n  </div>\n\n  <div style=\"margin-bottom: 20px;\">\n    <h3>API 测试</h3>\n    <el-button @click=\"testAPI\" type=\"primary\">测试租户API</el-button>\n    <el-button @click=\"resetLoading\" type=\"warning\">重置Loading状态</el-button>\n    <p v-if=\"apiResult\">API 结果: {{ apiResult }}</p>\n    <p v-if=\"apiError\" style=\"color: red;\">API 错误: {{ apiError }}</p>\n  </div>\n\n  <div style=\"margin-bottom: 20px;\">\n    <h3>元素检查</h3>\n    <el-button @click=\"checkElements\" type=\"info\">检查页面元素</el-button>\n    <div v-if=\"elementInfo\">\n      <pre>{{ elementInfo }}</pre>\n    </div>\n  </div>\n\n  <div style=\"margin-bottom: 20px;\">\n    <h3>测试表格</h3>\n    <el-table v-loading=\"testLoading\" :data=\"testData\" style=\"width: 100%\">\n      <el-table-column prop=\"name\" label=\"名称\"></el-table-column>\n      <el-table-column prop=\"value\" label=\"值\"></el-table-column>\n    </el-table>\n    <el-button @click=\"toggleTestLoading\" type=\"success\">切换测试Loading</el-button>\n  </div>\n</div>\n", null]}