{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue?vue&type=template&id=2b0a37d0&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue", "mtime": 1753782522395}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticStyle: {\n      padding: \"20px\"\n    }\n  }, [_c(\"h2\", [_vm._v(\"租户页面调试工具\")]), _c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"20px\"\n    }\n  }, [_c(\"h3\", [_vm._v(\"Loading 状态检查\")]), _c(\"p\", [_vm._v(\"Grid loading: \" + _vm._s(_vm.gridLoading))]), _c(\"p\", [_vm._v(\"Store loadingShow: \" + _vm._s(_vm.$store.state.common.loadingShow))]), _c(\"p\", [_vm._v(\"App loadings: \" + _vm._s(_vm.appLoadings))])]), _c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"20px\"\n    }\n  }, [_c(\"h3\", [_vm._v(\"API 测试\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.testAPI\n    }\n  }, [_vm._v(\"测试租户API\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"warning\"\n    },\n    on: {\n      click: _vm.resetLoading\n    }\n  }, [_vm._v(\"重置Loading状态\")]), _vm.apiResult ? _c(\"p\", [_vm._v(\"API 结果: \" + _vm._s(_vm.apiResult))]) : _vm._e(), _vm.apiError ? _c(\"p\", {\n    staticStyle: {\n      color: \"red\"\n    }\n  }, [_vm._v(\"API 错误: \" + _vm._s(_vm.apiError))]) : _vm._e()], 1), _c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"20px\"\n    }\n  }, [_c(\"h3\", [_vm._v(\"元素检查\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"info\"\n    },\n    on: {\n      click: _vm.checkElements\n    }\n  }, [_vm._v(\"检查页面元素\")]), _vm.elementInfo ? _c(\"div\", [_c(\"pre\", [_vm._v(_vm._s(_vm.elementInfo))])]) : _vm._e()], 1), _c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"20px\"\n    }\n  }, [_c(\"h3\", [_vm._v(\"测试表格\")]), _c(\"el-table\", {\n    directives: [{\n      name: \"loading\",\n      rawName: \"v-loading\",\n      value: _vm.testLoading,\n      expression: \"testLoading\"\n    }],\n    staticStyle: {\n      width: \"100%\"\n    },\n    attrs: {\n      data: _vm.testData\n    }\n  }, [_c(\"el-table-column\", {\n    attrs: {\n      prop: \"name\",\n      label: \"名称\"\n    }\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"value\",\n      label: \"值\"\n    }\n  })], 1), _c(\"el-button\", {\n    attrs: {\n      type: \"success\"\n    },\n    on: {\n      click: _vm.toggleTestLoading\n    }\n  }, [_vm._v(\"切换测试Loading\")])], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticStyle", "padding", "_v", "_s", "gridLoading", "$store", "state", "common", "loadingShow", "appLoadings", "attrs", "type", "on", "click", "testAPI", "resetLoading", "apiResult", "_e", "apiError", "color", "checkElements", "elementInfo", "directives", "name", "rawName", "value", "testLoading", "expression", "width", "data", "testData", "prop", "label", "toggleTestLoading", "staticRenderFns", "_withStripped"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/views/tenant/debug.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticStyle: { padding: \"20px\" } }, [\n    _c(\"h2\", [_vm._v(\"租户页面调试工具\")]),\n    _c(\"div\", { staticStyle: { \"margin-bottom\": \"20px\" } }, [\n      _c(\"h3\", [_vm._v(\"Loading 状态检查\")]),\n      _c(\"p\", [_vm._v(\"Grid loading: \" + _vm._s(_vm.gridLoading))]),\n      _c(\"p\", [\n        _vm._v(\n          \"Store loadingShow: \" + _vm._s(_vm.$store.state.common.loadingShow)\n        ),\n      ]),\n      _c(\"p\", [_vm._v(\"App loadings: \" + _vm._s(_vm.appLoadings))]),\n    ]),\n    _c(\n      \"div\",\n      { staticStyle: { \"margin-bottom\": \"20px\" } },\n      [\n        _c(\"h3\", [_vm._v(\"API 测试\")]),\n        _c(\n          \"el-button\",\n          { attrs: { type: \"primary\" }, on: { click: _vm.testAPI } },\n          [_vm._v(\"测试租户API\")]\n        ),\n        _c(\n          \"el-button\",\n          { attrs: { type: \"warning\" }, on: { click: _vm.resetLoading } },\n          [_vm._v(\"重置Loading状态\")]\n        ),\n        _vm.apiResult\n          ? _c(\"p\", [_vm._v(\"API 结果: \" + _vm._s(_vm.apiResult))])\n          : _vm._e(),\n        _vm.apiError\n          ? _c(\"p\", { staticStyle: { color: \"red\" } }, [\n              _vm._v(\"API 错误: \" + _vm._s(_vm.apiError)),\n            ])\n          : _vm._e(),\n      ],\n      1\n    ),\n    _c(\n      \"div\",\n      { staticStyle: { \"margin-bottom\": \"20px\" } },\n      [\n        _c(\"h3\", [_vm._v(\"元素检查\")]),\n        _c(\n          \"el-button\",\n          { attrs: { type: \"info\" }, on: { click: _vm.checkElements } },\n          [_vm._v(\"检查页面元素\")]\n        ),\n        _vm.elementInfo\n          ? _c(\"div\", [_c(\"pre\", [_vm._v(_vm._s(_vm.elementInfo))])])\n          : _vm._e(),\n      ],\n      1\n    ),\n    _c(\n      \"div\",\n      { staticStyle: { \"margin-bottom\": \"20px\" } },\n      [\n        _c(\"h3\", [_vm._v(\"测试表格\")]),\n        _c(\n          \"el-table\",\n          {\n            directives: [\n              {\n                name: \"loading\",\n                rawName: \"v-loading\",\n                value: _vm.testLoading,\n                expression: \"testLoading\",\n              },\n            ],\n            staticStyle: { width: \"100%\" },\n            attrs: { data: _vm.testData },\n          },\n          [\n            _c(\"el-table-column\", { attrs: { prop: \"name\", label: \"名称\" } }),\n            _c(\"el-table-column\", { attrs: { prop: \"value\", label: \"值\" } }),\n          ],\n          1\n        ),\n        _c(\n          \"el-button\",\n          { attrs: { type: \"success\" }, on: { click: _vm.toggleTestLoading } },\n          [_vm._v(\"切换测试Loading\")]\n        ),\n      ],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAE,CACrDH,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC9BJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;MAAE,eAAe,EAAE;IAAO;EAAE,CAAC,EAAE,CACtDF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,gBAAgB,GAAGL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC,EAC7DN,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACK,EAAE,CACJ,qBAAqB,GAAGL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACQ,MAAM,CAACC,KAAK,CAACC,MAAM,CAACC,WAAW,CACpE,CAAC,CACF,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,gBAAgB,GAAGL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,EACFX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;MAAE,eAAe,EAAE;IAAO;EAAE,CAAC,EAC5C,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BJ,EAAE,CACA,WAAW,EACX;IAAEY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEhB,GAAG,CAACiB;IAAQ;EAAE,CAAC,EAC1D,CAACjB,GAAG,CAACK,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDJ,EAAE,CACA,WAAW,EACX;IAAEY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEhB,GAAG,CAACkB;IAAa;EAAE,CAAC,EAC/D,CAAClB,GAAG,CAACK,EAAE,CAAC,aAAa,CAAC,CACxB,CAAC,EACDL,GAAG,CAACmB,SAAS,GACTlB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,UAAU,GAAGL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACmB,SAAS,CAAC,CAAC,CAAC,CAAC,GACrDnB,GAAG,CAACoB,EAAE,CAAC,CAAC,EACZpB,GAAG,CAACqB,QAAQ,GACRpB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;MAAEmB,KAAK,EAAE;IAAM;EAAE,CAAC,EAAE,CACzCtB,GAAG,CAACK,EAAE,CAAC,UAAU,GAAGL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACqB,QAAQ,CAAC,CAAC,CAC1C,CAAC,GACFrB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACDnB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;MAAE,eAAe,EAAE;IAAO;EAAE,CAAC,EAC5C,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BJ,EAAE,CACA,WAAW,EACX;IAAEY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAO,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEhB,GAAG,CAACuB;IAAc;EAAE,CAAC,EAC7D,CAACvB,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDL,GAAG,CAACwB,WAAW,GACXvB,EAAE,CAAC,KAAK,EAAE,CAACA,EAAE,CAAC,KAAK,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACwB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACzDxB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACDnB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;MAAE,eAAe,EAAE;IAAO;EAAE,CAAC,EAC5C,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BJ,EAAE,CACA,UAAU,EACV;IACEwB,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAE5B,GAAG,CAAC6B,WAAW;MACtBC,UAAU,EAAE;IACd,CAAC,CACF;IACD3B,WAAW,EAAE;MAAE4B,KAAK,EAAE;IAAO,CAAC;IAC9BlB,KAAK,EAAE;MAAEmB,IAAI,EAAEhC,GAAG,CAACiC;IAAS;EAC9B,CAAC,EACD,CACEhC,EAAE,CAAC,iBAAiB,EAAE;IAAEY,KAAK,EAAE;MAAEqB,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAK;EAAE,CAAC,CAAC,EAC/DlC,EAAE,CAAC,iBAAiB,EAAE;IAAEY,KAAK,EAAE;MAAEqB,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAI;EAAE,CAAC,CAAC,CAChE,EACD,CACF,CAAC,EACDlC,EAAE,CACA,WAAW,EACX;IAAEY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEhB,GAAG,CAACoC;IAAkB;EAAE,CAAC,EACpE,CAACpC,GAAG,CAACK,EAAE,CAAC,aAAa,CAAC,CACxB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIgC,eAAe,GAAG,EAAE;AACxBtC,MAAM,CAACuC,aAAa,GAAG,IAAI;AAE3B,SAASvC,MAAM,EAAEsC,eAAe", "ignoreList": []}]}