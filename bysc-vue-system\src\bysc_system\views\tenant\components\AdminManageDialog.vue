<!--
 * @Author: czw
 * @Date: 2024-01-01 00:00:00
 * @LastEditors: czw
 * @LastEditTime: 2024-01-01 00:00:00
 * @FilePath: \bysc-vue-system\src\bysc_system\views\tenant\components\AdminManageDialog.vue
 * @Description: 维护管理员弹窗组件
 *
 * Copyright (c) 2024 by czw/bysc, All Rights Reserved.
-->
<template>
  <el-dialog
    title="维护管理员"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="admin-manage-container">
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <el-button type="primary" size="small" @click="handleAdd">添加</el-button>
      </div>

      <!-- 管理员列表表格 -->
      <el-table
        :data="adminList"
        stripe
        style="width: 100%"
        v-loading="tableLoading"
      >
        <el-table-column
          prop="username"
          label="用户名"
          width="150"
          align="center"
        />
        <el-table-column
          prop="nickname"
          label="昵称"
          width="150"
          align="center"
        />
        <el-table-column
          prop="roleName"
          label="角色"
          width="150"
          align="center"
        />
        <el-table-column
          prop="orgName"
          label="组织"
          min-width="200"
          align="center"
        />
        <el-table-column
          label="操作"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
            >
              修改
            </el-button>
            <el-popconfirm
              title="确定要删除该管理员吗？"
              @confirm="handleDelete(scope.row)"
            >
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c"
                slot="reference"
              >
                删除
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑管理员表单弹窗 -->
    <el-dialog
      :title="formTitle"
      :visible.sync="formDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        :model="adminForm"
        :rules="formRules"
        ref="adminForm"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="adminForm.username"
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="adminForm.nickname"
            placeholder="请输入昵称"
          />
        </el-form-item>
        <el-form-item label="角色" prop="roleId">
          <el-select
            v-model="adminForm.roleId"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.roleName"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="组织" prop="orgId">
          <el-select
            v-model="adminForm.orgId"
            placeholder="请选择组织"
            style="width: 100%"
          >
            <el-option
              v-for="org in orgList"
              :key="org.id"
              :label="org.orgName"
              :value="org.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleFormCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleFormSubmit"
          :loading="formLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: 'AdminManageDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tenantInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      formDialogVisible: false,
      tableLoading: false,
      formLoading: false,
      isEdit: false,
      formTitle: '添加管理员',
      adminList: [],
      roleList: [],
      orgList: [],
      adminForm: {
        id: null,
        username: '',
        nickname: '',
        roleId: null,
        orgId: null,
        tenantId: null
      },
      formRules: {
        username: [
          {required: true, message: '请输入用户名', trigger: 'blur'}
        ],
        nickname: [
          {required: true, message: '请输入昵称', trigger: 'blur'}
        ],
        roleId: [
          {required: true, message: '请选择角色', trigger: 'change'}
        ],
        orgId: [
          {required: true, message: '请选择组织', trigger: 'change'}
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadAdminList();
        this.loadRoleList();
        this.loadOrgList();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    // 加载管理员列表
    loadAdminList() {
      if (!this.tenantInfo.id) {
        return;
      }

      this.tableLoading = true;
      this.$api['tenant/admin-list']({
        tenantId: this.tenantInfo.id
      }).then(data => {
        this.adminList = data || [];
      }).catch(error => {
        console.error('加载管理员列表失败:', error);
        this.$message.error('加载管理员列表失败');
      }).finally(() => {
        this.tableLoading = false;
      });
    },

    // 加载角色列表
    loadRoleList() {
      this.$api['system/role-list']().then(data => {
        this.roleList = data || [];
      }).catch(error => {
        console.error('加载角色列表失败:', error);
      });
    },

    // 加载组织列表
    loadOrgList() {
      this.$api['system/org-list']().then(data => {
        this.orgList = data || [];
      }).catch(error => {
        console.error('加载组织列表失败:', error);
      });
    },

    // 添加管理员
    handleAdd() {
      this.isEdit = false;
      this.formTitle = '添加管理员';
      this.adminForm = {
        id: null,
        username: '',
        nickname: '',
        roleId: null,
        orgId: null,
        tenantId: this.tenantInfo.id
      };
      this.formDialogVisible = true;
    },

    // 编辑管理员
    handleEdit(row) {
      this.isEdit = true;
      this.formTitle = '修改管理员';
      this.adminForm = {
        id: row.id,
        username: row.username,
        nickname: row.nickname,
        roleId: row.roleId,
        orgId: row.orgId,
        tenantId: this.tenantInfo.id
      };
      this.formDialogVisible = true;
    },

    // 删除管理员
    handleDelete(row) {
      this.$api['tenant/admin-delete']({
        id: row.id
      }).then(() => {
        this.$message.success('删除成功');
        this.loadAdminList();
      }).catch(error => {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
      });
    },

    // 表单提交
    handleFormSubmit() {
      this.$refs.adminForm.validate(valid => {
        if (valid) {
          this.formLoading = true;
          const api = this.isEdit ? 'tenant/admin-update' : 'tenant/admin-add';

          this.$api[api](this.adminForm).then(() => {
            this.$message.success(this.isEdit ? '修改成功' : '添加成功');
            this.formDialogVisible = false;
            this.loadAdminList();
          }).catch(error => {
            console.error('操作失败:', error);
            this.$message.error('操作失败');
          }).finally(() => {
            this.formLoading = false;
          });
        }
      });
    },

    // 表单取消
    handleFormCancel() {
      this.formDialogVisible = false;
      this.$refs.adminForm.resetFields();
    },

    // 关闭主弹窗
    handleClose() {
      this.dialogVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.admin-manage-container {
  .action-bar {
    margin-bottom: 16px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
