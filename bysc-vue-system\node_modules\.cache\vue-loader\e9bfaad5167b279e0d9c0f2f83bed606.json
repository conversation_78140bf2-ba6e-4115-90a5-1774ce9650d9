{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\TenantForm.vue?vue&type=template&id=683bba78&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\TenantForm.vue", "mtime": 1753782036906}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<div class=\"tenant-form\">\n  <el-form\n    :model=\"formData\"\n    :rules=\"rules\"\n    ref=\"tenantForm\"\n    label-width=\"120px\"\n    class=\"tenant-form-content\"\n  >\n    <el-form-item label=\"租户编码：\" prop=\"tenantCode\">\n      <el-input\n        :value=\"formData.tenantCode\"\n        @input=\"handleFieldChange('tenantCode', $event.trim())\"\n        placeholder=\"请输入租户编码\"\n        maxlength=\"32\"\n        :disabled=\"isEdit\"\n      ></el-input>\n    </el-form-item>\n\n    <el-form-item label=\"*租户名称：\" prop=\"tenantName\">\n      <el-input\n        :value=\"formData.tenantName\"\n        @input=\"handleFieldChange('tenantName', $event.trim())\"\n        placeholder=\"请输入租户名称\"\n        maxlength=\"50\"\n      ></el-input>\n    </el-form-item>\n\n    <el-form-item label=\"租户管理员：\" prop=\"tenantAdmin\">\n      <el-input\n        :value=\"formData.tenantAdmin\"\n        @input=\"handleFieldChange('tenantAdmin', $event.trim())\"\n        placeholder=\"请输入租户管理员\"\n        maxlength=\"50\"\n      ></el-input>\n    </el-form-item>\n\n    <el-form-item label=\"租户备注：\" prop=\"comments\">\n      <el-input\n        :value=\"formData.comments\"\n        @input=\"handleFieldChange('comments', $event.trim())\"\n        type=\"textarea\"\n        :rows=\"4\"\n        placeholder=\"请输入租户备注\"\n        maxlength=\"200\"\n        show-word-limit\n      ></el-input>\n    </el-form-item>\n\n    <el-form-item label=\"是否选中：\" prop=\"isSelected\">\n      <el-switch\n        :value=\"formData.isSelected\"\n        @change=\"handleFieldChange('isSelected', $event)\"\n        active-text=\"是\"\n        inactive-text=\"否\"\n      ></el-switch>\n    </el-form-item>\n  </el-form>\n\n  <div class=\"form-actions\">\n    <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">\n      {{ isEdit ? '保存' : '添加' }}\n    </el-button>\n    <el-button @click=\"handleCancel\">取消</el-button>\n  </div>\n</div>\n", null]}