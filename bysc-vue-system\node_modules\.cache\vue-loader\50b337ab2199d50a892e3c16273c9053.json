{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\Grid.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\components\\Grid.vue", "mtime": 1753782463897}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": 1745221301271}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./Grid.vue?vue&type=template&id=2de7b714\"\nimport script from \"./Grid.vue?vue&type=script&lang=js\"\nexport * from \"./Grid.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2de7b714')) {\n      api.createRecord('2de7b714', component.options)\n    } else {\n      api.reload('2de7b714', component.options)\n    }\n    module.hot.accept(\"./Grid.vue?vue&type=template&id=2de7b714\", function () {\n      api.rerender('2de7b714', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/Grid.vue\"\nexport default component.exports"]}