{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue", "mtime": 1753782522395}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\nimport \"regenerator-runtime/runtime\";\nimport _asyncToGenerator from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nexport default {\n  name: 'TenantDebug',\n  data: function data() {\n    return {\n      gridLoading: false,\n      appLoadings: false,\n      testLoading: false,\n      apiResult: null,\n      apiError: null,\n      elementInfo: null,\n      testData: [{\n        name: '测试1',\n        value: '值1'\n      }, {\n        name: '测试2',\n        value: '值2'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.checkLoadingStates();\n    // 定期检查状态\n    this.timer = setInterval(function () {\n      _this.checkLoadingStates();\n    }, 1000);\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    checkLoadingStates: function checkLoadingStates() {\n      // 检查各种 loading 状态\n      this.appLoadings = this.$parent && this.$parent.loadings;\n\n      // 检查是否有 Grid 组件\n      var gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        this.gridLoading = gridComponent.loading;\n      }\n    },\n    testAPI: function () {\n      var _testAPI = _asyncToGenerator(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {\n        var result;\n        return regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              this.apiResult = null;\n              this.apiError = null;\n              _context.prev = 2;\n              console.log('开始测试 API...');\n              _context.next = 6;\n              return this.$api['tenant/tenant-page']({\n                current: 1,\n                limit: 10,\n                param: {}\n              });\n            case 6:\n              result = _context.sent;\n              console.log('API 测试成功:', result);\n              this.apiResult = JSON.stringify(result, null, 2);\n              _context.next = 15;\n              break;\n            case 11:\n              _context.prev = 11;\n              _context.t0 = _context[\"catch\"](2);\n              console.error('API 测试失败:', _context.t0);\n              this.apiError = _context.t0.message || '未知错误';\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this, [[2, 11]]);\n      }));\n      function testAPI() {\n        return _testAPI.apply(this, arguments);\n      }\n      return testAPI;\n    }(),\n    resetLoading: function resetLoading() {\n      // 重置各种 loading 状态\n      this.testLoading = false;\n      if (this.$parent && this.$parent.hideLoadings) {\n        this.$parent.hideLoadings();\n      }\n\n      // 重置 Grid loading\n      var gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        gridComponent.loading = false;\n      }\n      console.log('已重置所有 loading 状态');\n    },\n    toggleTestLoading: function toggleTestLoading() {\n      this.testLoading = !this.testLoading;\n    },\n    checkElements: function checkElements() {\n      var info = {\n        loadingElements: [],\n        overlayElements: [],\n        maskElements: []\n      };\n\n      // 检查页面中的 loading 相关元素\n      var loadingEls = document.querySelectorAll('[class*=\"loading\"]');\n      loadingEls.forEach(function (el, index) {\n        info.loadingElements.push({\n          index: index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n\n      // 检查遮罩层元素\n      var overlayEls = document.querySelectorAll('.el-loading-mask, .el-overlay, [class*=\"mask\"]');\n      overlayEls.forEach(function (el, index) {\n        info.overlayElements.push({\n          index: index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n\n      // 检查可能的蒙层元素\n      var allEls = document.querySelectorAll('*');\n      allEls.forEach(function (el, index) {\n        var style = window.getComputedStyle(el);\n        if (style.position === 'fixed' || style.position === 'absolute') {\n          if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)' && (style.zIndex > 100 || el.offsetWidth > window.innerWidth * 0.8)) {\n            info.maskElements.push({\n              index: index,\n              tagName: el.tagName,\n              className: el.className,\n              position: style.position,\n              zIndex: style.zIndex,\n              backgroundColor: style.backgroundColor,\n              width: el.offsetWidth,\n              height: el.offsetHeight\n            });\n          }\n        }\n      });\n      this.elementInfo = JSON.stringify(info, null, 2);\n      console.log('页面元素检查结果:', info);\n    }\n  }\n};", {"version": 3, "names": ["name", "data", "gridLoading", "appLoadings", "testLoading", "apiResult", "apiError", "elementInfo", "testData", "value", "mounted", "_this", "checkLoadingStates", "timer", "setInterval", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "$parent", "loadings", "gridComponent", "$refs", "grid", "loading", "testAPI", "_testAPI", "_asyncToGenerator", "regeneratorRuntime", "mark", "_callee", "result", "wrap", "_callee$", "_context", "prev", "next", "console", "log", "$api", "current", "limit", "param", "sent", "JSON", "stringify", "t0", "error", "message", "stop", "apply", "arguments", "resetLoading", "hideLoadings", "toggleTestLoading", "checkElements", "info", "loadingElements", "overlayElements", "maskElements", "loadingEls", "document", "querySelectorAll", "for<PERSON>ach", "el", "index", "push", "className", "style", "cssText", "visible", "offsetParent", "overlayEls", "allEls", "window", "getComputedStyle", "position", "backgroundColor", "zIndex", "offsetWidth", "innerWidth", "tagName", "width", "height", "offsetHeight"], "sources": ["src/bysc_system/views/tenant/debug.vue"], "sourcesContent": ["<template>\n  <div style=\"padding: 20px;\">\n    <h2>租户页面调试工具</h2>\n    \n    <div style=\"margin-bottom: 20px;\">\n      <h3>Loading 状态检查</h3>\n      <p>Grid loading: {{ gridLoading }}</p>\n      <p>Store loadingShow: {{ $store.state.common.loadingShow }}</p>\n      <p>App loadings: {{ appLoadings }}</p>\n    </div>\n\n    <div style=\"margin-bottom: 20px;\">\n      <h3>API 测试</h3>\n      <el-button @click=\"testAPI\" type=\"primary\">测试租户API</el-button>\n      <el-button @click=\"resetLoading\" type=\"warning\">重置Loading状态</el-button>\n      <p v-if=\"apiResult\">API 结果: {{ apiResult }}</p>\n      <p v-if=\"apiError\" style=\"color: red;\">API 错误: {{ apiError }}</p>\n    </div>\n\n    <div style=\"margin-bottom: 20px;\">\n      <h3>元素检查</h3>\n      <el-button @click=\"checkElements\" type=\"info\">检查页面元素</el-button>\n      <div v-if=\"elementInfo\">\n        <pre>{{ elementInfo }}</pre>\n      </div>\n    </div>\n\n    <div style=\"margin-bottom: 20px;\">\n      <h3>测试表格</h3>\n      <el-table v-loading=\"testLoading\" :data=\"testData\" style=\"width: 100%\">\n        <el-table-column prop=\"name\" label=\"名称\"></el-table-column>\n        <el-table-column prop=\"value\" label=\"值\"></el-table-column>\n      </el-table>\n      <el-button @click=\"toggleTestLoading\" type=\"success\">切换测试Loading</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TenantDebug',\n  data() {\n    return {\n      gridLoading: false,\n      appLoadings: false,\n      testLoading: false,\n      apiResult: null,\n      apiError: null,\n      elementInfo: null,\n      testData: [\n        { name: '测试1', value: '值1' },\n        { name: '测试2', value: '值2' }\n      ]\n    };\n  },\n  mounted() {\n    this.checkLoadingStates();\n    // 定期检查状态\n    this.timer = setInterval(() => {\n      this.checkLoadingStates();\n    }, 1000);\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    checkLoadingStates() {\n      // 检查各种 loading 状态\n      this.appLoadings = this.$parent && this.$parent.loadings;\n      \n      // 检查是否有 Grid 组件\n      const gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        this.gridLoading = gridComponent.loading;\n      }\n    },\n    \n    async testAPI() {\n      this.apiResult = null;\n      this.apiError = null;\n      \n      try {\n        console.log('开始测试 API...');\n        const result = await this.$api['tenant/tenant-page']({\n          current: 1,\n          limit: 10,\n          param: {}\n        });\n        console.log('API 测试成功:', result);\n        this.apiResult = JSON.stringify(result, null, 2);\n      } catch (error) {\n        console.error('API 测试失败:', error);\n        this.apiError = error.message || '未知错误';\n      }\n    },\n    \n    resetLoading() {\n      // 重置各种 loading 状态\n      this.testLoading = false;\n      \n      if (this.$parent && this.$parent.hideLoadings) {\n        this.$parent.hideLoadings();\n      }\n      \n      // 重置 Grid loading\n      const gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        gridComponent.loading = false;\n      }\n      \n      console.log('已重置所有 loading 状态');\n    },\n    \n    toggleTestLoading() {\n      this.testLoading = !this.testLoading;\n    },\n    \n    checkElements() {\n      const info = {\n        loadingElements: [],\n        overlayElements: [],\n        maskElements: []\n      };\n      \n      // 检查页面中的 loading 相关元素\n      const loadingEls = document.querySelectorAll('[class*=\"loading\"]');\n      loadingEls.forEach((el, index) => {\n        info.loadingElements.push({\n          index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n      \n      // 检查遮罩层元素\n      const overlayEls = document.querySelectorAll('.el-loading-mask, .el-overlay, [class*=\"mask\"]');\n      overlayEls.forEach((el, index) => {\n        info.overlayElements.push({\n          index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n      \n      // 检查可能的蒙层元素\n      const allEls = document.querySelectorAll('*');\n      allEls.forEach((el, index) => {\n        const style = window.getComputedStyle(el);\n        if (style.position === 'fixed' || style.position === 'absolute') {\n          if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)' && \n              (style.zIndex > 100 || el.offsetWidth > window.innerWidth * 0.8)) {\n            info.maskElements.push({\n              index,\n              tagName: el.tagName,\n              className: el.className,\n              position: style.position,\n              zIndex: style.zIndex,\n              backgroundColor: style.backgroundColor,\n              width: el.offsetWidth,\n              height: el.offsetHeight\n            });\n          }\n        }\n      });\n      \n      this.elementInfo = JSON.stringify(info, null, 2);\n      console.log('页面元素检查结果:', info);\n    }\n  }\n};\n</script>\n\n<style scoped>\npre {\n  background: #f5f5f5;\n  padding: 10px;\n  border-radius: 4px;\n  max-height: 300px;\n  overflow-y: auto;\n  font-size: 12px;\n}\n</style>\n"], "mappings": ";;;AAuCA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,SAAA;MACAC,QAAA;MACAC,WAAA;MACAC,QAAA,GACA;QAAAR,IAAA;QAAAS,KAAA;MAAA,GACA;QAAAT,IAAA;QAAAS,KAAA;MAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,kBAAA;IACA;IACA,KAAAC,KAAA,GAAAC,WAAA;MACAH,KAAA,CAAAC,kBAAA;IACA;EACA;EACAG,aAAA,WAAAA,cAAA;IACA,SAAAF,KAAA;MACAG,aAAA,MAAAH,KAAA;IACA;EACA;EACAI,OAAA;IACAL,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAT,WAAA,QAAAe,OAAA,SAAAA,OAAA,CAAAC,QAAA;;MAEA;MACA,IAAAC,aAAA,QAAAF,OAAA,SAAAA,OAAA,CAAAG,KAAA,SAAAH,OAAA,CAAAG,KAAA,CAAAC,IAAA;MACA,IAAAF,aAAA;QACA,KAAAlB,WAAA,GAAAkB,aAAA,CAAAG,OAAA;MACA;IACA;IAEAC,OAAA;MAAA,IAAAC,QAAA,GAAAC,iBAAA,cAAAC,kBAAA,CAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA;QAAA,OAAAH,kBAAA,CAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA,KAAA9B,SAAA;cACA,KAAAC,QAAA;cAAA2B,QAAA,CAAAC,IAAA;cAGAE,OAAA,CAAAC,GAAA;cAAAJ,QAAA,CAAAE,IAAA;cAAA,OACA,KAAAG,IAAA;gBACAC,OAAA;gBACAC,KAAA;gBACAC,KAAA;cACA;YAAA;cAJAX,MAAA,GAAAG,QAAA,CAAAS,IAAA;cAKAN,OAAA,CAAAC,GAAA,cAAAP,MAAA;cACA,KAAAzB,SAAA,GAAAsC,IAAA,CAAAC,SAAA,CAAAd,MAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAY,EAAA,GAAAZ,QAAA;cAEAG,OAAA,CAAAU,KAAA,cAAAb,QAAA,CAAAY,EAAA;cACA,KAAAvC,QAAA,GAAA2B,QAAA,CAAAY,EAAA,CAAAE,OAAA;YAAA;YAAA;cAAA,OAAAd,QAAA,CAAAe,IAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;MAAA,SAfAL,QAAA;QAAA,OAAAC,QAAA,CAAAwB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAA1B,OAAA;IAAA;IAmBA2B,YAAA,WAAAA,aAAA;MACA;MACA,KAAA/C,WAAA;MAEA,SAAAc,OAAA,SAAAA,OAAA,CAAAkC,YAAA;QACA,KAAAlC,OAAA,CAAAkC,YAAA;MACA;;MAEA;MACA,IAAAhC,aAAA,QAAAF,OAAA,SAAAA,OAAA,CAAAG,KAAA,SAAAH,OAAA,CAAAG,KAAA,CAAAC,IAAA;MACA,IAAAF,aAAA;QACAA,aAAA,CAAAG,OAAA;MACA;MAEAa,OAAA,CAAAC,GAAA;IACA;IAEAgB,iBAAA,WAAAA,kBAAA;MACA,KAAAjD,WAAA,SAAAA,WAAA;IACA;IAEAkD,aAAA,WAAAA,cAAA;MACA,IAAAC,IAAA;QACAC,eAAA;QACAC,eAAA;QACAC,YAAA;MACA;;MAEA;MACA,IAAAC,UAAA,GAAAC,QAAA,CAAAC,gBAAA;MACAF,UAAA,CAAAG,OAAA,WAAAC,EAAA,EAAAC,KAAA;QACAT,IAAA,CAAAC,eAAA,CAAAS,IAAA;UACAD,KAAA,EAAAA,KAAA;UACAE,SAAA,EAAAH,EAAA,CAAAG,SAAA;UACAC,KAAA,EAAAJ,EAAA,CAAAI,KAAA,CAAAC,OAAA;UACAC,OAAA,EAAAN,EAAA,CAAAO,YAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,GAAAX,QAAA,CAAAC,gBAAA;MACAU,UAAA,CAAAT,OAAA,WAAAC,EAAA,EAAAC,KAAA;QACAT,IAAA,CAAAE,eAAA,CAAAQ,IAAA;UACAD,KAAA,EAAAA,KAAA;UACAE,SAAA,EAAAH,EAAA,CAAAG,SAAA;UACAC,KAAA,EAAAJ,EAAA,CAAAI,KAAA,CAAAC,OAAA;UACAC,OAAA,EAAAN,EAAA,CAAAO,YAAA;QACA;MACA;;MAEA;MACA,IAAAE,MAAA,GAAAZ,QAAA,CAAAC,gBAAA;MACAW,MAAA,CAAAV,OAAA,WAAAC,EAAA,EAAAC,KAAA;QACA,IAAAG,KAAA,GAAAM,MAAA,CAAAC,gBAAA,CAAAX,EAAA;QACA,IAAAI,KAAA,CAAAQ,QAAA,gBAAAR,KAAA,CAAAQ,QAAA;UACA,IAAAR,KAAA,CAAAS,eAAA,IAAAT,KAAA,CAAAS,eAAA,4BACAT,KAAA,CAAAU,MAAA,UAAAd,EAAA,CAAAe,WAAA,GAAAL,MAAA,CAAAM,UAAA;YACAxB,IAAA,CAAAG,YAAA,CAAAO,IAAA;cACAD,KAAA,EAAAA,KAAA;cACAgB,OAAA,EAAAjB,EAAA,CAAAiB,OAAA;cACAd,SAAA,EAAAH,EAAA,CAAAG,SAAA;cACAS,QAAA,EAAAR,KAAA,CAAAQ,QAAA;cACAE,MAAA,EAAAV,KAAA,CAAAU,MAAA;cACAD,eAAA,EAAAT,KAAA,CAAAS,eAAA;cACAK,KAAA,EAAAlB,EAAA,CAAAe,WAAA;cACAI,MAAA,EAAAnB,EAAA,CAAAoB;YACA;UACA;QACA;MACA;MAEA,KAAA5E,WAAA,GAAAoC,IAAA,CAAAC,SAAA,CAAAW,IAAA;MACAnB,OAAA,CAAAC,GAAA,cAAAkB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}