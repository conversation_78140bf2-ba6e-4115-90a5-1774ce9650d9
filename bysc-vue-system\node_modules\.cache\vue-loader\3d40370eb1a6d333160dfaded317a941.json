{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue?vue&type=style&index=0&id=60b06436&lang=less&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue", "mtime": 1753839332756}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\css-loader\\index.js", "mtime": 1745221300128}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745221314654}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745221303798}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745221307121}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n.admin-manage-container {\n  padding: 20px;\n  height: 100%;\n\n  .action-bar {\n    margin-bottom: 16px;\n    padding-bottom: 16px;\n    border-bottom: 1px solid #ebeef5;\n  }\n\n  .el-table {\n    border: 1px solid #ebeef5;\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 16px;\n  border-top: 1px solid #ebeef5;\n}\n\n// 抽屉样式优化\n:deep(.el-drawer) {\n  .el-drawer__header {\n    padding: 20px 20px 0 20px;\n    margin-bottom: 0;\n  }\n\n  .el-drawer__body {\n    padding: 0;\n    overflow-y: auto;\n  }\n}\n", {"version": 3, "sources": ["AdminManageDialog.vue"], "names": [], "mappings": ";AA4bA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AdminManageDialog.vue", "sourceRoot": "src/bysc_system/views/tenant/components", "sourcesContent": ["<!--\n * @Author: czw\n * @Date: 2024-01-01 00:00:00\n * @LastEditors: czw\n * @LastEditTime: 2024-01-01 00:00:00\n * @FilePath: \\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue\n * @Description: 维护管理员弹窗组件\n *\n * Copyright (c) 2024 by czw/bysc, All Rights Reserved.\n-->\n<template>\n  <el-drawer\n    title=\"维护管理员\"\n    :visible.sync=\"dialogVisible\"\n    direction=\"rtl\"\n    size=\"60%\"\n    :close-on-press-escape=\"false\"\n    :wrapperClosable=\"false\"\n    @close=\"handleClose\"\n  >\n    <div class=\"admin-manage-container\">\n      <!-- 操作按钮区域 -->\n      <div class=\"action-bar\">\n        <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\">添加</el-button>\n      </div>\n\n      <!-- 管理员列表表格 -->\n      <el-table\n        :data=\"adminList\"\n        stripe\n        style=\"width: 100%\"\n        v-loading=\"tableLoading\"\n      >\n        <el-table-column\n          prop=\"username\"\n          label=\"用户名\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"nickname\"\n          label=\"昵称\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"roleName\"\n          label=\"角色\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"orgName\"\n          label=\"组织\"\n          min-width=\"200\"\n          align=\"center\"\n        />\n        <el-table-column\n          label=\"操作\"\n          width=\"150\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"handleEdit(scope.row)\"\n            >\n              修改\n            </el-button>\n            <el-popconfirm\n              title=\"确定要删除该管理员吗？\"\n              @confirm=\"handleDelete(scope.row)\"\n            >\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                style=\"color: #f56c6c\"\n                slot=\"reference\"\n              >\n                删除\n              </el-button>\n            </el-popconfirm>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 添加/编辑管理员表单弹窗 -->\n    <el-dialog\n      :title=\"formTitle\"\n      :visible.sync=\"formDialogVisible\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      append-to-body\n    >\n      <el-form\n        :model=\"adminForm\"\n        :rules=\"formRules\"\n        ref=\"adminForm\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input\n            v-model=\"adminForm.username\"\n            placeholder=\"请输入用户名\"\n            :disabled=\"isEdit\"\n          />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input\n            v-model=\"adminForm.nickname\"\n            placeholder=\"请输入昵称\"\n          />\n        </el-form-item>\n\n        <!-- 添加模式下显示的字段 -->\n        <template v-if=\"!isEdit\">\n          <el-form-item label=\"密码\" prop=\"password\">\n            <el-input\n              v-model=\"adminForm.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              show-password\n            />\n          </el-form-item>\n          <el-form-item label=\"再次确认密码\" prop=\"confirmPassword\">\n            <el-input\n              v-model=\"adminForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              show-password\n            />\n          </el-form-item>\n          <el-form-item label=\"角色\" prop=\"roleId\">\n            <el-select\n              v-model=\"adminForm.roleId\"\n              placeholder=\"请选择角色\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"role in roleList\"\n                :key=\"role.id\"\n                :label=\"role.name\"\n                :value=\"role.id\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"组织\" prop=\"orgId\">\n            <el-select\n              v-model=\"adminForm.orgId\"\n              placeholder=\"请选择组织\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"org in orgList\"\n                :key=\"org.id\"\n                :label=\"org.name\"\n                :value=\"org.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </template>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleFormCancel\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleFormSubmit\"\n          :loading=\"formLoading\"\n        >\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </el-drawer>\n</template>\n\n<script>\nexport default {\n  name: 'AdminManageDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    tenantInfo: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      formDialogVisible: false,\n      tableLoading: false,\n      formLoading: false,\n      isEdit: false,\n      formTitle: '添加管理员',\n      adminList: [\n        {\n          id: 1,\n          username: 'admin001',\n          nickname: '系统管理员',\n          roleId: 1,\n          roleName: '超级管理员',\n          orgId: 1,\n          orgName: '总部',\n          tenantId: 1\n        },\n        {\n          id: 2,\n          username: 'manager001',\n          nickname: '业务经理',\n          roleId: 2,\n          roleName: '业务管理员',\n          orgId: 2,\n          orgName: '业务部',\n          tenantId: 1\n        }\n      ],\n      roleList: [\n        {\n          id: 1,\n          name: '超级管理员',\n          code: 'super_admin'\n        },\n        {\n          id: 2,\n          name: '业务管理员',\n          code: 'business_admin'\n        },\n        {\n          id: 3,\n          name: '普通用户',\n          code: 'normal_user'\n        }\n      ],\n      orgList: [\n        {\n          id: 1,\n          name: '总部',\n          code: 'headquarters'\n        },\n        {\n          id: 2,\n          name: '业务部',\n          code: 'business_dept'\n        },\n        {\n          id: 3,\n          name: '技术部',\n          code: 'tech_dept'\n        }\n      ],\n      adminForm: {\n        id: null,\n        username: '',\n        nickname: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        orgId: null,\n        tenantId: null\n      },\n      formRules: {\n        username: [\n          {required: true, message: '请输入用户名', trigger: 'blur'}\n        ],\n        nickname: [\n          {required: true, message: '请输入昵称', trigger: 'blur'}\n        ],\n        password: [\n          {required: true, message: '请输入密码', trigger: 'blur'},\n          {min: 6, message: '密码长度不能少于6位', trigger: 'blur'}\n        ],\n        confirmPassword: [\n          {required: true, message: '请再次输入密码', trigger: 'blur'},\n          {\n            validator: (rule, value, callback) => {\n              if (value !== this.adminForm.password) {\n                callback(new Error('两次输入的密码不一致'));\n              } else {\n                callback();\n              }\n            },\n            trigger: 'blur'\n          }\n        ],\n        roleId: [\n          {required: true, message: '请选择角色', trigger: 'change'}\n        ],\n        orgId: [\n          {required: true, message: '请选择组织', trigger: 'change'}\n        ]\n      }\n    };\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        this.loadAdminList();\n        this.loadRoleList();\n        this.loadOrgList();\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    // 加载管理员列表\n    loadAdminList() {\n      if (!this.tenantInfo.id) {\n        return;\n      }\n\n      this.tableLoading = true;\n      // 使用假数据模拟API调用\n      setTimeout(() => {\n        // 假数据已经在data中定义，这里不需要重新赋值\n        this.tableLoading = false;\n      }, 500);\n    },\n\n    // 加载角色列表\n    loadRoleList() {\n      // 使用假数据，已在data中定义\n      console.log('角色列表已加载');\n    },\n\n    // 加载组织列表\n    loadOrgList() {\n      // 使用假数据，已在data中定义\n      console.log('组织列表已加载');\n    },\n\n    // 添加管理员\n    handleAdd() {\n      this.isEdit = false;\n      this.formTitle = '添加管理员';\n      this.adminForm = {\n        id: null,\n        username: '',\n        nickname: '',\n        password: '',\n        confirmPassword: '',\n        roleId: null,\n        orgId: null,\n        tenantId: this.tenantInfo.id,\n        isSelected: false // 默认为否\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 编辑管理员\n    handleEdit(row) {\n      this.isEdit = true;\n      this.formTitle = '编辑管理员';\n      this.adminForm = {\n        id: row.id,\n        username: row.username,\n        nickname: row.nickname,\n        password: '', // 编辑时不显示密码字段\n        confirmPassword: '',\n        roleId: row.roleId,\n        orgId: row.orgId,\n        tenantId: this.tenantInfo.id,\n        isSelected: row.isSelected || false // 从现有数据中获取，默认为false\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 删除管理员\n    handleDelete(row) {\n      // 模拟删除操作\n      const index = this.adminList.findIndex(item => item.id === row.id);\n      if (index !== -1) {\n        this.adminList.splice(index, 1);\n        this.$message.success('删除成功');\n      } else {\n        this.$message.error('删除失败');\n      }\n    },\n\n    // 表单提交\n    handleFormSubmit() {\n      this.$refs.adminForm.validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n\n          // 模拟API调用\n          setTimeout(() => {\n            if (this.isEdit) {\n              // 编辑模式：更新现有数据\n              const index = this.adminList.findIndex(item => item.id === this.adminForm.id);\n              if (index !== -1) {\n                const roleInfo = this.roleList.find(role => role.id === this.adminForm.roleId);\n                const orgInfo = this.orgList.find(org => org.id === this.adminForm.orgId);\n\n                this.adminList.splice(index, 1, {\n                  ...this.adminForm,\n                  roleName: roleInfo ? roleInfo.name : '',\n                  orgName: orgInfo ? orgInfo.name : ''\n                });\n              }\n            } else {\n              // 添加模式：添加新数据\n              const roleInfo = this.roleList.find(role => role.id === this.adminForm.roleId);\n              const orgInfo = this.orgList.find(org => org.id === this.adminForm.orgId);\n\n              const newAdmin = {\n                ...this.adminForm,\n                id: Date.now(), // 使用时间戳作为临时ID\n                roleName: roleInfo ? roleInfo.name : '',\n                orgName: orgInfo ? orgInfo.name : ''\n              };\n              this.adminList.push(newAdmin);\n            }\n\n            this.$message.success(this.isEdit ? '修改成功' : '添加成功');\n            this.formDialogVisible = false;\n            this.formLoading = false;\n          }, 500);\n        }\n      });\n    },\n\n    // 表单取消\n    handleFormCancel() {\n      this.formDialogVisible = false;\n      this.$refs.adminForm.resetFields();\n    },\n\n    // 关闭主弹窗\n    handleClose() {\n      this.dialogVisible = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.admin-manage-container {\n  padding: 20px;\n  height: 100%;\n\n  .action-bar {\n    margin-bottom: 16px;\n    padding-bottom: 16px;\n    border-bottom: 1px solid #ebeef5;\n  }\n\n  .el-table {\n    border: 1px solid #ebeef5;\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 16px;\n  border-top: 1px solid #ebeef5;\n}\n\n// 抽屉样式优化\n:deep(.el-drawer) {\n  .el-drawer__header {\n    padding: 20px 20px 0 20px;\n    margin-bottom: 0;\n  }\n\n  .el-drawer__body {\n    padding: 0;\n    overflow-y: auto;\n  }\n}\n</style>\n"]}]}