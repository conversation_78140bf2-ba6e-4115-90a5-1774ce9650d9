{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue", "mtime": 1753783638356}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["export default {\n  name: 'AdminManageDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    tenantInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      dialogVisible: false,\n      formDialogVisible: false,\n      tableLoading: false,\n      formLoading: false,\n      isEdit: false,\n      formTitle: '添加管理员',\n      adminList: [],\n      roleList: [],\n      orgList: [],\n      adminForm: {\n        id: null,\n        username: '',\n        nickname: '',\n        roleId: null,\n        orgId: null,\n        tenantId: null\n      },\n      formRules: {\n        username: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        nickname: [{\n          required: true,\n          message: '请输入昵称',\n          trigger: 'blur'\n        }],\n        roleId: [{\n          required: true,\n          message: '请选择角色',\n          trigger: 'change'\n        }],\n        orgId: [{\n          required: true,\n          message: '请选择组织',\n          trigger: 'change'\n        }]\n      }\n    };\n  },\n  watch: {\n    visible: function visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        this.loadAdminList();\n        this.loadRoleList();\n        this.loadOrgList();\n      }\n    },\n    dialogVisible: function dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    // 加载管理员列表\n    loadAdminList: function loadAdminList() {\n      var _this = this;\n      if (!this.tenantInfo.id) {\n        return;\n      }\n      this.tableLoading = true;\n      this.$api['tenant/admin-list']({\n        tenantId: this.tenantInfo.id\n      }).then(function (data) {\n        _this.adminList = data || [];\n      }).catch(function (error) {\n        console.error('加载管理员列表失败:', error);\n        _this.$message.error('加载管理员列表失败');\n      }).finally(function () {\n        _this.tableLoading = false;\n      });\n    },\n    // 加载角色列表\n    loadRoleList: function loadRoleList() {\n      var _this2 = this;\n      this.$api['system/role-list']().then(function (data) {\n        _this2.roleList = data || [];\n      }).catch(function (error) {\n        console.error('加载角色列表失败:', error);\n      });\n    },\n    // 加载组织列表\n    loadOrgList: function loadOrgList() {\n      var _this3 = this;\n      this.$api['system/org-list']().then(function (data) {\n        _this3.orgList = data || [];\n      }).catch(function (error) {\n        console.error('加载组织列表失败:', error);\n      });\n    },\n    // 添加管理员\n    handleAdd: function handleAdd() {\n      this.isEdit = false;\n      this.formTitle = '添加管理员';\n      this.adminForm = {\n        id: null,\n        username: '',\n        nickname: '',\n        roleId: null,\n        orgId: null,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n    // 编辑管理员\n    handleEdit: function handleEdit(row) {\n      this.isEdit = true;\n      this.formTitle = '修改管理员';\n      this.adminForm = {\n        id: row.id,\n        username: row.username,\n        nickname: row.nickname,\n        roleId: row.roleId,\n        orgId: row.orgId,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n    // 删除管理员\n    handleDelete: function handleDelete(row) {\n      var _this4 = this;\n      this.$api['tenant/admin-delete']({\n        id: row.id\n      }).then(function () {\n        _this4.$message.success('删除成功');\n        _this4.loadAdminList();\n      }).catch(function (error) {\n        console.error('删除失败:', error);\n        _this4.$message.error('删除失败');\n      });\n    },\n    // 表单提交\n    handleFormSubmit: function handleFormSubmit() {\n      var _this5 = this;\n      this.$refs.adminForm.validate(function (valid) {\n        if (valid) {\n          _this5.formLoading = true;\n          var api = _this5.isEdit ? 'tenant/admin-update' : 'tenant/admin-add';\n          _this5.$api[api](_this5.adminForm).then(function () {\n            _this5.$message.success(_this5.isEdit ? '修改成功' : '添加成功');\n            _this5.formDialogVisible = false;\n            _this5.loadAdminList();\n          }).catch(function (error) {\n            console.error('操作失败:', error);\n            _this5.$message.error('操作失败');\n          }).finally(function () {\n            _this5.formLoading = false;\n          });\n        }\n      });\n    },\n    // 表单取消\n    handleFormCancel: function handleFormCancel() {\n      this.formDialogVisible = false;\n      this.$refs.adminForm.resetFields();\n    },\n    // 关闭主弹窗\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "tenantInfo", "Object", "data", "dialogVisible", "formDialogVisible", "tableLoading", "formLoading", "isEdit", "formTitle", "adminList", "roleList", "orgList", "adminForm", "id", "username", "nickname", "roleId", "orgId", "tenantId", "formRules", "required", "message", "trigger", "watch", "val", "loadAdminList", "loadRoleList", "loadOrgList", "$emit", "methods", "_this", "$api", "then", "catch", "error", "console", "$message", "finally", "_this2", "_this3", "handleAdd", "handleEdit", "row", "handleDelete", "_this4", "success", "handleFormSubmit", "_this5", "$refs", "validate", "valid", "api", "handleFormCancel", "resetFields", "handleClose"], "sources": ["src/bysc_system/views/tenant/components/AdminManageDialog.vue"], "sourcesContent": ["<!--\n * @Author: czw\n * @Date: 2024-01-01 00:00:00\n * @LastEditors: czw\n * @LastEditTime: 2024-01-01 00:00:00\n * @FilePath: \\bysc-vue-system\\src\\bysc_system\\views\\tenant\\components\\AdminManageDialog.vue\n * @Description: 维护管理员弹窗组件\n *\n * Copyright (c) 2024 by czw/bysc, All Rights Reserved.\n-->\n<template>\n  <el-dialog\n    title=\"维护管理员\"\n    :visible.sync=\"dialogVisible\"\n    width=\"80%\"\n    :close-on-click-modal=\"false\"\n    :close-on-press-escape=\"false\"\n    @close=\"handleClose\"\n  >\n    <div class=\"admin-manage-container\">\n      <!-- 操作按钮区域 -->\n      <div class=\"action-bar\">\n        <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\">添加</el-button>\n      </div>\n\n      <!-- 管理员列表表格 -->\n      <el-table\n        :data=\"adminList\"\n        stripe\n        style=\"width: 100%\"\n        v-loading=\"tableLoading\"\n      >\n        <el-table-column\n          prop=\"username\"\n          label=\"用户名\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"nickname\"\n          label=\"昵称\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"roleName\"\n          label=\"角色\"\n          width=\"150\"\n          align=\"center\"\n        />\n        <el-table-column\n          prop=\"orgName\"\n          label=\"组织\"\n          min-width=\"200\"\n          align=\"center\"\n        />\n        <el-table-column\n          label=\"操作\"\n          width=\"150\"\n          align=\"center\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"handleEdit(scope.row)\"\n            >\n              修改\n            </el-button>\n            <el-popconfirm\n              title=\"确定要删除该管理员吗？\"\n              @confirm=\"handleDelete(scope.row)\"\n            >\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                style=\"color: #f56c6c\"\n                slot=\"reference\"\n              >\n                删除\n              </el-button>\n            </el-popconfirm>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 添加/编辑管理员表单弹窗 -->\n    <el-dialog\n      :title=\"formTitle\"\n      :visible.sync=\"formDialogVisible\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      append-to-body\n    >\n      <el-form\n        :model=\"adminForm\"\n        :rules=\"formRules\"\n        ref=\"adminForm\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input\n            v-model=\"adminForm.username\"\n            placeholder=\"请输入用户名\"\n            :disabled=\"isEdit\"\n          />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input\n            v-model=\"adminForm.nickname\"\n            placeholder=\"请输入昵称\"\n          />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"roleId\">\n          <el-select\n            v-model=\"adminForm.roleId\"\n            placeholder=\"请选择角色\"\n            style=\"width: 100%\"\n          >\n            <el-option\n              v-for=\"role in roleList\"\n              :key=\"role.id\"\n              :label=\"role.roleName\"\n              :value=\"role.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"组织\" prop=\"orgId\">\n          <el-select\n            v-model=\"adminForm.orgId\"\n            placeholder=\"请选择组织\"\n            style=\"width: 100%\"\n          >\n            <el-option\n              v-for=\"org in orgList\"\n              :key=\"org.id\"\n              :label=\"org.orgName\"\n              :value=\"org.id\"\n            />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleFormCancel\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleFormSubmit\"\n          :loading=\"formLoading\"\n        >\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'AdminManageDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    tenantInfo: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      formDialogVisible: false,\n      tableLoading: false,\n      formLoading: false,\n      isEdit: false,\n      formTitle: '添加管理员',\n      adminList: [],\n      roleList: [],\n      orgList: [],\n      adminForm: {\n        id: null,\n        username: '',\n        nickname: '',\n        roleId: null,\n        orgId: null,\n        tenantId: null\n      },\n      formRules: {\n        username: [\n          {required: true, message: '请输入用户名', trigger: 'blur'}\n        ],\n        nickname: [\n          {required: true, message: '请输入昵称', trigger: 'blur'}\n        ],\n        roleId: [\n          {required: true, message: '请选择角色', trigger: 'change'}\n        ],\n        orgId: [\n          {required: true, message: '请选择组织', trigger: 'change'}\n        ]\n      }\n    };\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        this.loadAdminList();\n        this.loadRoleList();\n        this.loadOrgList();\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    // 加载管理员列表\n    loadAdminList() {\n      if (!this.tenantInfo.id) {\n        return;\n      }\n\n      this.tableLoading = true;\n      this.$api['tenant/admin-list']({\n        tenantId: this.tenantInfo.id\n      }).then(data => {\n        this.adminList = data || [];\n      }).catch(error => {\n        console.error('加载管理员列表失败:', error);\n        this.$message.error('加载管理员列表失败');\n      }).finally(() => {\n        this.tableLoading = false;\n      });\n    },\n\n    // 加载角色列表\n    loadRoleList() {\n      this.$api['system/role-list']().then(data => {\n        this.roleList = data || [];\n      }).catch(error => {\n        console.error('加载角色列表失败:', error);\n      });\n    },\n\n    // 加载组织列表\n    loadOrgList() {\n      this.$api['system/org-list']().then(data => {\n        this.orgList = data || [];\n      }).catch(error => {\n        console.error('加载组织列表失败:', error);\n      });\n    },\n\n    // 添加管理员\n    handleAdd() {\n      this.isEdit = false;\n      this.formTitle = '添加管理员';\n      this.adminForm = {\n        id: null,\n        username: '',\n        nickname: '',\n        roleId: null,\n        orgId: null,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 编辑管理员\n    handleEdit(row) {\n      this.isEdit = true;\n      this.formTitle = '修改管理员';\n      this.adminForm = {\n        id: row.id,\n        username: row.username,\n        nickname: row.nickname,\n        roleId: row.roleId,\n        orgId: row.orgId,\n        tenantId: this.tenantInfo.id\n      };\n      this.formDialogVisible = true;\n    },\n\n    // 删除管理员\n    handleDelete(row) {\n      this.$api['tenant/admin-delete']({\n        id: row.id\n      }).then(() => {\n        this.$message.success('删除成功');\n        this.loadAdminList();\n      }).catch(error => {\n        console.error('删除失败:', error);\n        this.$message.error('删除失败');\n      });\n    },\n\n    // 表单提交\n    handleFormSubmit() {\n      this.$refs.adminForm.validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n          const api = this.isEdit ? 'tenant/admin-update' : 'tenant/admin-add';\n\n          this.$api[api](this.adminForm).then(() => {\n            this.$message.success(this.isEdit ? '修改成功' : '添加成功');\n            this.formDialogVisible = false;\n            this.loadAdminList();\n          }).catch(error => {\n            console.error('操作失败:', error);\n            this.$message.error('操作失败');\n          }).finally(() => {\n            this.formLoading = false;\n          });\n        }\n      });\n    },\n\n    // 表单取消\n    handleFormCancel() {\n      this.formDialogVisible = false;\n      this.$refs.adminForm.resetFields();\n    },\n\n    // 关闭主弹窗\n    handleClose() {\n      this.dialogVisible = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.admin-manage-container {\n  .action-bar {\n    margin-bottom: 16px;\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": "AA8JA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,WAAA;MACAC,MAAA;MACAC,SAAA;MACAC,SAAA;MACAC,QAAA;MACAC,OAAA;MACAC,SAAA;QACAC,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACAC,SAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,KAAA;IACA3B,OAAA,WAAAA,QAAA4B,GAAA;MACA,KAAArB,aAAA,GAAAqB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,aAAA;QACA,KAAAC,YAAA;QACA,KAAAC,WAAA;MACA;IACA;IACAxB,aAAA,WAAAA,cAAAqB,GAAA;MACA,KAAAI,KAAA,mBAAAJ,GAAA;IACA;EACA;EACAK,OAAA;IACA;IACAJ,aAAA,WAAAA,cAAA;MAAA,IAAAK,KAAA;MACA,UAAA9B,UAAA,CAAAa,EAAA;QACA;MACA;MAEA,KAAAR,YAAA;MACA,KAAA0B,IAAA;QACAb,QAAA,OAAAlB,UAAA,CAAAa;MACA,GAAAmB,IAAA,WAAA9B,IAAA;QACA4B,KAAA,CAAArB,SAAA,GAAAP,IAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;QACAJ,KAAA,CAAAM,QAAA,CAAAF,KAAA;MACA,GAAAG,OAAA;QACAP,KAAA,CAAAzB,YAAA;MACA;IACA;IAEA;IACAqB,YAAA,WAAAA,aAAA;MAAA,IAAAY,MAAA;MACA,KAAAP,IAAA,uBAAAC,IAAA,WAAA9B,IAAA;QACAoC,MAAA,CAAA5B,QAAA,GAAAR,IAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAP,WAAA,WAAAA,YAAA;MAAA,IAAAY,MAAA;MACA,KAAAR,IAAA,sBAAAC,IAAA,WAAA9B,IAAA;QACAqC,MAAA,CAAA5B,OAAA,GAAAT,IAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAM,SAAA,WAAAA,UAAA;MACA,KAAAjC,MAAA;MACA,KAAAC,SAAA;MACA,KAAAI,SAAA;QACAC,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,QAAA,OAAAlB,UAAA,CAAAa;MACA;MACA,KAAAT,iBAAA;IACA;IAEA;IACAqC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAnC,MAAA;MACA,KAAAC,SAAA;MACA,KAAAI,SAAA;QACAC,EAAA,EAAA6B,GAAA,CAAA7B,EAAA;QACAC,QAAA,EAAA4B,GAAA,CAAA5B,QAAA;QACAC,QAAA,EAAA2B,GAAA,CAAA3B,QAAA;QACAC,MAAA,EAAA0B,GAAA,CAAA1B,MAAA;QACAC,KAAA,EAAAyB,GAAA,CAAAzB,KAAA;QACAC,QAAA,OAAAlB,UAAA,CAAAa;MACA;MACA,KAAAT,iBAAA;IACA;IAEA;IACAuC,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,KAAAb,IAAA;QACAlB,EAAA,EAAA6B,GAAA,CAAA7B;MACA,GAAAmB,IAAA;QACAY,MAAA,CAAAR,QAAA,CAAAS,OAAA;QACAD,MAAA,CAAAnB,aAAA;MACA,GAAAQ,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACAU,MAAA,CAAAR,QAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAY,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAApC,SAAA,CAAAqC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAzC,WAAA;UACA,IAAA6C,GAAA,GAAAJ,MAAA,CAAAxC,MAAA;UAEAwC,MAAA,CAAAhB,IAAA,CAAAoB,GAAA,EAAAJ,MAAA,CAAAnC,SAAA,EAAAoB,IAAA;YACAe,MAAA,CAAAX,QAAA,CAAAS,OAAA,CAAAE,MAAA,CAAAxC,MAAA;YACAwC,MAAA,CAAA3C,iBAAA;YACA2C,MAAA,CAAAtB,aAAA;UACA,GAAAQ,KAAA,WAAAC,KAAA;YACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;YACAa,MAAA,CAAAX,QAAA,CAAAF,KAAA;UACA,GAAAG,OAAA;YACAU,MAAA,CAAAzC,WAAA;UACA;QACA;MACA;IACA;IAEA;IACA8C,gBAAA,WAAAA,iBAAA;MACA,KAAAhD,iBAAA;MACA,KAAA4C,KAAA,CAAApC,SAAA,CAAAyC,WAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAnD,aAAA;IACA;EACA;AACA", "ignoreList": []}]}