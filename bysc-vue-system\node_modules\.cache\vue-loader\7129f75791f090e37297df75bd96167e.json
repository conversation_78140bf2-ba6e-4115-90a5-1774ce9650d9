{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\debug.vue", "mtime": 1753782522395}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\nexport default {\n  name: 'TenantDebug',\n  data() {\n    return {\n      gridLoading: false,\n      appLoadings: false,\n      testLoading: false,\n      apiResult: null,\n      apiError: null,\n      elementInfo: null,\n      testData: [\n        { name: '测试1', value: '值1' },\n        { name: '测试2', value: '值2' }\n      ]\n    };\n  },\n  mounted() {\n    this.checkLoadingStates();\n    // 定期检查状态\n    this.timer = setInterval(() => {\n      this.checkLoadingStates();\n    }, 1000);\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    checkLoadingStates() {\n      // 检查各种 loading 状态\n      this.appLoadings = this.$parent && this.$parent.loadings;\n      \n      // 检查是否有 Grid 组件\n      const gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        this.gridLoading = gridComponent.loading;\n      }\n    },\n    \n    async testAPI() {\n      this.apiResult = null;\n      this.apiError = null;\n      \n      try {\n        console.log('开始测试 API...');\n        const result = await this.$api['tenant/tenant-page']({\n          current: 1,\n          limit: 10,\n          param: {}\n        });\n        console.log('API 测试成功:', result);\n        this.apiResult = JSON.stringify(result, null, 2);\n      } catch (error) {\n        console.error('API 测试失败:', error);\n        this.apiError = error.message || '未知错误';\n      }\n    },\n    \n    resetLoading() {\n      // 重置各种 loading 状态\n      this.testLoading = false;\n      \n      if (this.$parent && this.$parent.hideLoadings) {\n        this.$parent.hideLoadings();\n      }\n      \n      // 重置 Grid loading\n      const gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        gridComponent.loading = false;\n      }\n      \n      console.log('已重置所有 loading 状态');\n    },\n    \n    toggleTestLoading() {\n      this.testLoading = !this.testLoading;\n    },\n    \n    checkElements() {\n      const info = {\n        loadingElements: [],\n        overlayElements: [],\n        maskElements: []\n      };\n      \n      // 检查页面中的 loading 相关元素\n      const loadingEls = document.querySelectorAll('[class*=\"loading\"]');\n      loadingEls.forEach((el, index) => {\n        info.loadingElements.push({\n          index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n      \n      // 检查遮罩层元素\n      const overlayEls = document.querySelectorAll('.el-loading-mask, .el-overlay, [class*=\"mask\"]');\n      overlayEls.forEach((el, index) => {\n        info.overlayElements.push({\n          index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n      \n      // 检查可能的蒙层元素\n      const allEls = document.querySelectorAll('*');\n      allEls.forEach((el, index) => {\n        const style = window.getComputedStyle(el);\n        if (style.position === 'fixed' || style.position === 'absolute') {\n          if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)' && \n              (style.zIndex > 100 || el.offsetWidth > window.innerWidth * 0.8)) {\n            info.maskElements.push({\n              index,\n              tagName: el.tagName,\n              className: el.className,\n              position: style.position,\n              zIndex: style.zIndex,\n              backgroundColor: style.backgroundColor,\n              width: el.offsetWidth,\n              height: el.offsetHeight\n            });\n          }\n        }\n      });\n      \n      this.elementInfo = JSON.stringify(info, null, 2);\n      console.log('页面元素检查结果:', info);\n    }\n  }\n};\n", {"version": 3, "sources": ["debug.vue"], "names": [], "mappings": ";AAuCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "debug.vue", "sourceRoot": "src/bysc_system/views/tenant", "sourcesContent": ["<template>\n  <div style=\"padding: 20px;\">\n    <h2>租户页面调试工具</h2>\n    \n    <div style=\"margin-bottom: 20px;\">\n      <h3>Loading 状态检查</h3>\n      <p>Grid loading: {{ gridLoading }}</p>\n      <p>Store loadingShow: {{ $store.state.common.loadingShow }}</p>\n      <p>App loadings: {{ appLoadings }}</p>\n    </div>\n\n    <div style=\"margin-bottom: 20px;\">\n      <h3>API 测试</h3>\n      <el-button @click=\"testAPI\" type=\"primary\">测试租户API</el-button>\n      <el-button @click=\"resetLoading\" type=\"warning\">重置Loading状态</el-button>\n      <p v-if=\"apiResult\">API 结果: {{ apiResult }}</p>\n      <p v-if=\"apiError\" style=\"color: red;\">API 错误: {{ apiError }}</p>\n    </div>\n\n    <div style=\"margin-bottom: 20px;\">\n      <h3>元素检查</h3>\n      <el-button @click=\"checkElements\" type=\"info\">检查页面元素</el-button>\n      <div v-if=\"elementInfo\">\n        <pre>{{ elementInfo }}</pre>\n      </div>\n    </div>\n\n    <div style=\"margin-bottom: 20px;\">\n      <h3>测试表格</h3>\n      <el-table v-loading=\"testLoading\" :data=\"testData\" style=\"width: 100%\">\n        <el-table-column prop=\"name\" label=\"名称\"></el-table-column>\n        <el-table-column prop=\"value\" label=\"值\"></el-table-column>\n      </el-table>\n      <el-button @click=\"toggleTestLoading\" type=\"success\">切换测试Loading</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TenantDebug',\n  data() {\n    return {\n      gridLoading: false,\n      appLoadings: false,\n      testLoading: false,\n      apiResult: null,\n      apiError: null,\n      elementInfo: null,\n      testData: [\n        { name: '测试1', value: '值1' },\n        { name: '测试2', value: '值2' }\n      ]\n    };\n  },\n  mounted() {\n    this.checkLoadingStates();\n    // 定期检查状态\n    this.timer = setInterval(() => {\n      this.checkLoadingStates();\n    }, 1000);\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    checkLoadingStates() {\n      // 检查各种 loading 状态\n      this.appLoadings = this.$parent && this.$parent.loadings;\n      \n      // 检查是否有 Grid 组件\n      const gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        this.gridLoading = gridComponent.loading;\n      }\n    },\n    \n    async testAPI() {\n      this.apiResult = null;\n      this.apiError = null;\n      \n      try {\n        console.log('开始测试 API...');\n        const result = await this.$api['tenant/tenant-page']({\n          current: 1,\n          limit: 10,\n          param: {}\n        });\n        console.log('API 测试成功:', result);\n        this.apiResult = JSON.stringify(result, null, 2);\n      } catch (error) {\n        console.error('API 测试失败:', error);\n        this.apiError = error.message || '未知错误';\n      }\n    },\n    \n    resetLoading() {\n      // 重置各种 loading 状态\n      this.testLoading = false;\n      \n      if (this.$parent && this.$parent.hideLoadings) {\n        this.$parent.hideLoadings();\n      }\n      \n      // 重置 Grid loading\n      const gridComponent = this.$parent && this.$parent.$refs && this.$parent.$refs.grid;\n      if (gridComponent) {\n        gridComponent.loading = false;\n      }\n      \n      console.log('已重置所有 loading 状态');\n    },\n    \n    toggleTestLoading() {\n      this.testLoading = !this.testLoading;\n    },\n    \n    checkElements() {\n      const info = {\n        loadingElements: [],\n        overlayElements: [],\n        maskElements: []\n      };\n      \n      // 检查页面中的 loading 相关元素\n      const loadingEls = document.querySelectorAll('[class*=\"loading\"]');\n      loadingEls.forEach((el, index) => {\n        info.loadingElements.push({\n          index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n      \n      // 检查遮罩层元素\n      const overlayEls = document.querySelectorAll('.el-loading-mask, .el-overlay, [class*=\"mask\"]');\n      overlayEls.forEach((el, index) => {\n        info.overlayElements.push({\n          index,\n          className: el.className,\n          style: el.style.cssText,\n          visible: el.offsetParent !== null\n        });\n      });\n      \n      // 检查可能的蒙层元素\n      const allEls = document.querySelectorAll('*');\n      allEls.forEach((el, index) => {\n        const style = window.getComputedStyle(el);\n        if (style.position === 'fixed' || style.position === 'absolute') {\n          if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)' && \n              (style.zIndex > 100 || el.offsetWidth > window.innerWidth * 0.8)) {\n            info.maskElements.push({\n              index,\n              tagName: el.tagName,\n              className: el.className,\n              position: style.position,\n              zIndex: style.zIndex,\n              backgroundColor: style.backgroundColor,\n              width: el.offsetWidth,\n              height: el.offsetHeight\n            });\n          }\n        }\n      });\n      \n      this.elementInfo = JSON.stringify(info, null, 2);\n      console.log('页面元素检查结果:', info);\n    }\n  }\n};\n</script>\n\n<style scoped>\npre {\n  background: #f5f5f5;\n  padding: 10px;\n  border-radius: 4px;\n  max-height: 300px;\n  overflow-y: auto;\n  font-size: 12px;\n}\n</style>\n"]}]}